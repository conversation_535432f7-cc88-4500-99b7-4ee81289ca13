<?php

namespace Tests\Feature;

use App\Models\Currency;
use App\Models\Order;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ShoppingCart;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CheckoutTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $product;
    protected $cart;

    protected function setUp(): void
    {
        parent::setUp();

        // Create default currency
        Currency::create([
            'code' => 'ZAR',
            'name' => 'South African Rand',
            'symbol' => 'R',
            'exchange_rate' => 1.0000,
            'is_default' => true,
            'is_active' => true,
        ]);

        $this->user = User::factory()->create();

        $category = ProductCategory::factory()->create([
            'is_active' => true,
        ]);

        $this->product = Product::factory()->create([
            'name' => 'Test Product',
            'price' => 100.00,
            'is_active' => true,
            'track_inventory' => false,
        ]);

        $this->product->categories()->attach($category);

        $this->cart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'subtotal' => 100.00,
            'tax_amount' => 15.00,
            'shipping_amount' => 0.00,
            'total' => 115.00,
            'expires_at' => now()->addDays(30),
        ]);

        $this->cart->items()->create([
            'product_id' => $this->product->id,
            'quantity' => 1,
            'price' => 100.00,
            'total' => 100.00,
        ]);
    }

    /** @test */
    public function it_displays_checkout_page_with_cart()
    {
        $this->actingAs($this->user)
            ->get('/checkout')
            ->assertStatus(200)
            ->assertViewIs('pages.checkout.index')
            ->assertViewHas('cart')
            ->assertViewHas('user');
    }

    /** @test */
    public function it_redirects_to_shop_if_cart_is_empty()
    {
        // Clear cart items
        $this->cart->items()->delete();

        $this->actingAs($this->user)
            ->get('/checkout')
            ->assertRedirect(route('shop.index'))
            ->assertSessionHas('error', 'Your cart is empty.');
    }

    /** @test */
    public function it_can_process_checkout_with_valid_data()
    {
        $checkoutData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '+27123456789',
            'billing_address_line_1' => '123 Main St',
            'billing_address_line_2' => '',
            'billing_city' => 'Cape Town',
            'billing_state' => 'Western Cape',
            'billing_postal_code' => '8001',
            'billing_country' => 'ZA',
            'ship_to_different_address' => false,
            'payment_method' => 'stripe',
            'terms_accepted' => true,
        ];

        $this->actingAs($this->user)
            ->post('/checkout/process', $checkoutData)
            ->assertRedirect()
            ->assertSessionHas('success');

        // Check that order was created
        $order = Order::where('user_id', $this->user->id)->first();
        $this->assertNotNull($order);
        $this->assertEquals('<EMAIL>', $order->email);
        $this->assertEquals('pending', $order->payment_status);
        $this->assertEquals('pending', $order->status);
        $this->assertEquals(115.00, $order->total_amount);
    }

    /** @test */
    public function it_validates_required_checkout_fields()
    {
        $this->actingAs($this->user)
            ->post('/checkout/process', [])
            ->assertSessionHasErrors([
                'first_name',
                'last_name',
                'email',
                'billing_address_line_1',
                'billing_city',
                'billing_state',
                'billing_postal_code',
                'billing_country',
                'payment_method',
                'terms_accepted',
            ]);
    }

    /** @test */
    public function it_validates_email_format()
    {
        $checkoutData = [
            'email' => 'invalid-email',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'billing_address_line_1' => '123 Main St',
            'billing_city' => 'Cape Town',
            'billing_state' => 'Western Cape',
            'billing_postal_code' => '8001',
            'billing_country' => 'South Africa',
            'shipping_first_name' => 'John',
            'shipping_last_name' => 'Doe',
            'shipping_address_line_1' => '123 Main St',
            'shipping_city' => 'Cape Town',
            'shipping_state' => 'Western Cape',
            'shipping_postal_code' => '8001',
            'shipping_country' => 'South Africa',
            'payment_method' => 'stripe',
            'terms_accepted' => true,
        ];

        $this->actingAs($this->user)
            ->post('/checkout/process', $checkoutData)
            ->assertSessionHasErrors(['email']);
    }

    /** @test */
    public function it_requires_terms_acceptance()
    {
        $checkoutData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'billing_address_line_1' => '123 Main St',
            'billing_city' => 'Cape Town',
            'billing_state' => 'Western Cape',
            'billing_postal_code' => '8001',
            'billing_country' => 'South Africa',
            'shipping_first_name' => 'John',
            'shipping_last_name' => 'Doe',
            'shipping_address_line_1' => '123 Main St',
            'shipping_city' => 'Cape Town',
            'shipping_state' => 'Western Cape',
            'shipping_postal_code' => '8001',
            'shipping_country' => 'South Africa',
            'payment_method' => 'stripe',
            'terms_accepted' => false,
        ];

        $this->actingAs($this->user)
            ->post('/checkout/process', $checkoutData)
            ->assertSessionHasErrors(['terms_accepted']);
    }

    /** @test */
    public function it_creates_order_items_from_cart()
    {
        // Add another product to cart
        $product2 = Product::factory()->create([
            'price' => 50.00,
            'is_active' => true,
            'track_inventory' => false,
        ]);

        $this->cart->items()->create([
            'product_id' => $product2->id,
            'quantity' => 2,
            'price' => 50.00,
            'total' => 100.00,
        ]);

        $this->cart->update(['subtotal' => 200.00, 'total' => 230.00]);

        $checkoutData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'billing_address_line_1' => '123 Main St',
            'billing_address_line_2' => '',
            'billing_city' => 'Cape Town',
            'billing_state' => 'Western Cape',
            'billing_postal_code' => '8001',
            'billing_country' => 'ZA',
            'ship_to_different_address' => false,
            'payment_method' => 'stripe',
            'terms_accepted' => true,
        ];

        $this->actingAs($this->user)
            ->post('/checkout/process', $checkoutData);

        $order = Order::where('user_id', $this->user->id)->first();
        $this->assertEquals(2, $order->items->count());
        
        $firstItem = $order->items->where('product_id', $this->product->id)->first();
        $this->assertEquals(1, $firstItem->quantity);
        $this->assertEquals(100.00, $firstItem->unit_price);

        $secondItem = $order->items->where('product_id', $product2->id)->first();
        $this->assertEquals(2, $secondItem->quantity);
        $this->assertEquals(50.00, $secondItem->unit_price);
    }

    /** @test */
    public function it_displays_payment_page_for_pending_order()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'payment_status' => 'pending',
            'total_amount' => 115.00,
        ]);

        $this->actingAs($this->user)
            ->get("/checkout/payment/{$order->uuid}")
            ->assertStatus(200)
            ->assertViewIs('pages.checkout.payment')
            ->assertViewHas('order');
    }

    /** @test */
    public function it_redirects_paid_orders_from_payment_page()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'payment_status' => 'paid',
        ]);

        $this->actingAs($this->user)
            ->get("/checkout/payment/{$order->uuid}")
            ->assertRedirect(route('orders.show', $order->uuid));
    }

    /** @test */
    public function it_displays_success_page_after_payment()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'payment_status' => 'paid',
            'status' => 'processing',
        ]);

        $order->items()->create([
            'product_id' => $this->product->id,
            'quantity' => 1,
            'unit_price' => 100.00,
            'total_price' => 100.00,
            'product_snapshot' => [
                'product_name' => $this->product->name,
            ],
        ]);

        $this->actingAs($this->user)
            ->get("/checkout/success/{$order->uuid}")
            ->assertStatus(200)
            ->assertViewIs('pages.checkout.success')
            ->assertViewHas('order');
    }

    /** @test */
    public function it_prevents_unauthorized_access_to_orders()
    {
        $otherUser = User::factory()->create();
        $order = Order::factory()->create([
            'user_id' => $otherUser->id,
        ]);

        $this->actingAs($this->user)
            ->get("/checkout/success/{$order->uuid}")
            ->assertStatus(403);
    }
}
